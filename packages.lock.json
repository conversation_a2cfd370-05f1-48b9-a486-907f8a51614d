{"version": 1, "dependencies": {"net9.0": {"Exercism.Tests.xunit.v3": {"type": "Direct", "requested": "[0.1.0-beta1, )", "resolved": "0.1.0-beta1", "contentHash": "XjVtQWWxmHDDj7UMdkPKpBFFKnsW0tkBhlyJSfFFh+fWwGemyyJwJYhdsvWhiKKCY7zItB+mI/o0OQtOKQxUhA==", "dependencies": {"xunit.v3.extensibility.core": "1.1.0"}}, "Microsoft.NET.Test.Sdk": {"type": "Direct", "requested": "[17.12.0, )", "resolved": "17.12.0", "contentHash": "kt/PKBZ91rFCWxVIJZSgVLk+YR+4KxTuHf799ho8WNiK5ZQpJNAEZCAWX86vcKrs+DiYjiibpYKdGZP6+/N17w==", "dependencies": {"Microsoft.CodeCoverage": "17.12.0", "Microsoft.TestPlatform.TestHost": "17.12.0"}}, "xunit.runner.visualstudio": {"type": "Direct", "requested": "[3.0.1, )", "resolved": "3.0.1", "contentHash": "lbyYtsBxA8Pz8kaf5Xn/Mj1mL9z2nlBWdZhqFaj66nxXBa4JwiTDm4eGcpSMet6du9TOWI6bfha+gQR6+IHawg=="}, "xunit.v3": {"type": "Direct", "requested": "[1.1.0, )", "resolved": "1.1.0", "contentHash": "1ckSz5GVswlM9TCk5bGdHOjnYwqAWjkeqxckoHawQIA8sTeuN+RCBUypCi5A/Um0XlczRx5TjAK5W6BbN0HLcQ==", "dependencies": {"xunit.analyzers": "1.20.0", "xunit.v3.assert": "[1.1.0]", "xunit.v3.core": "[1.1.0]"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg=="}, "Microsoft.CodeCoverage": {"type": "Transitive", "resolved": "17.12.0", "contentHash": "4svMznBd5JM21JIG2xZKGNanAHNXplxf/kQDFfLHXQ3OnpJkayRK/TjacFjA+EYmoyuNXHo/sOETEfcYtAzIrA=="}, "Microsoft.Testing.Extensions.TrxReport.Abstractions": {"type": "Transitive", "resolved": "1.5.3", "contentHash": "h34zKNpGyni66VH738mRHeXSnf3klSShUdavUWNhSfWICUUi5aXeI0LBvoX/ad93N0+9xBDU3Fyi6WfxrwKQGw==", "dependencies": {"Microsoft.Testing.Platform": "1.5.3"}}, "Microsoft.Testing.Platform": {"type": "Transitive", "resolved": "1.5.3", "contentHash": "WqJydnJ99dEKtquR9HwINz104ehWJKTXbQQrydGatlLRw14bmsx0pa8+E6KUXMYXZAimN0swWlDmcJGjjW4TIg=="}, "Microsoft.Testing.Platform.MSBuild": {"type": "Transitive", "resolved": "1.5.3", "contentHash": "bOtpRMSPeT5YLQo+NNY8EtdNTphAUcmALjW4ABU7P0rb6yR2XAZau3TzNieLmR3lRuwudguWzzBhgcLRXwZh0A==", "dependencies": {"Microsoft.Testing.Platform": "1.5.3"}}, "Microsoft.TestPlatform.ObjectModel": {"type": "Transitive", "resolved": "17.12.0", "contentHash": "TDqkTKLfQuAaPcEb3pDDWnh7b3SyZF+/W9OZvWFp6eJCIiiYFdSB6taE2I6tWrFw5ywhzOb6sreoGJTI6m3rSQ==", "dependencies": {"System.Reflection.Metadata": "1.6.0"}}, "Microsoft.TestPlatform.TestHost": {"type": "Transitive", "resolved": "17.12.0", "contentHash": "MiPEJQNyADfwZ4pJNpQex+t9/jOClBGMiCiVVFuELCMSX2nmNfvUor3uFVxNNCg30uxDP8JDYfPnMXQzsfzYyg==", "dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.12.0", "Newtonsoft.Json": "13.0.1"}}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "13.0.1", "contentHash": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A=="}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg=="}, "System.Memory": {"type": "Transitive", "resolved": "4.6.0", "contentHash": "OEkbBQoklHngJ8UD8ez2AERSk2g+/qpAaSWWCBFbpH727HxDq5ydVkuncBaKcKfwRqXGWx64dS6G1SUScMsitg=="}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "1.6.0", "contentHash": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ=="}, "xunit.analyzers": {"type": "Transitive", "resolved": "1.20.0", "contentHash": "HElev2E9vFbPxwKRQtpCSSzLOu8M/N9EWBCB37v7SRx6z4Lbj19FxfLEig3v9jiI6s4b0l2uena91nEsTWl9jA=="}, "xunit.v3.assert": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "4D+eM08ImfhA+zLbRzi8HA4qsT98zDxgaCD7vCg8yFesokKsgSsqWsAmImHFjVymGVhVS7WFGb19d6v1k9i0xQ==", "dependencies": {"System.Collections.Immutable": "8.0.0", "System.Memory": "4.6.0"}}, "xunit.v3.common": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "Cq55z8pC7fOkfj+3TB/YQ6OW96qWqxKiMd15CtkIl37VtV9EsiUL4B4HsR6VLJCzkk7cBiXQ1ABVIcp3TCm6HQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}}, "xunit.v3.core": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "kXP/1d3jnQ2m4skcdM3gSMmubI6P747D6KVswzeedysgFkLj2xJlfo7p7slsmtEnp8BZb8X6D92Hssd/UtVPMw==", "dependencies": {"Microsoft.Testing.Platform.MSBuild": "1.5.3", "xunit.v3.extensibility.core": "[1.1.0]", "xunit.v3.runner.inproc.console": "[1.1.0]"}}, "xunit.v3.extensibility.core": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "AeQbbYN001x0c+B9pqwml6jZPovHz8O/sOp7jmrjz90rUzz/QPal12SlHLKYszR44CMnW4MsDam3RYT5pkYUxw==", "dependencies": {"xunit.v3.common": "[1.1.0]"}}, "xunit.v3.runner.common": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "Q81J0VPuu8fpF+/1CIjThqKKUjnqh0TQrLlD0iORkF75KdsOV+iGWT8c3AVuY96kDoxXxkTf0ZvJsK6o9osc1A==", "dependencies": {"xunit.v3.common": "[1.1.0]"}}, "xunit.v3.runner.inproc.console": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "lX/4TwIJe9ysCd5dqLk/Doq8ieYaZGivgf95xR59wRuSV+nHzHnyhpjXfaPUp8nkncUH1rOmJ85o1KebipisXQ==", "dependencies": {"Microsoft.Testing.Extensions.TrxReport.Abstractions": "1.5.3", "Microsoft.Testing.Platform": "1.5.3", "xunit.v3.extensibility.core": "[1.1.0]", "xunit.v3.runner.common": "[1.1.0]"}}}}}